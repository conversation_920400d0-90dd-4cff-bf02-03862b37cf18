import discord
from discord.ext import commands
import datetime

def register_update_commands(bot):
    """Regis<PERSON>ert den !lordupdate Be<PERSON>hl"""



    @bot.tree.command(name="changelog", description="<PERSON><PERSON><PERSON> das Changelog für eine bestimmte Version")
    async def changelog_slash(interaction: discord.Interaction, version: str = None):
        """Zeigt das Changelog als Slash-Befehl"""
        # Changelog-System verwenden
        from changelog import send_version_changelog, send_changelog_overview, changelog_data

        if version:
            # Spezifische Version anzeigen
            if version in changelog_data:
                await send_version_changelog(interaction, version)
            else:
                embed = discord.Embed(
                    title="❌ Version Not Found",
                    description=f"Version `{version}` not found in changelog.\n\nAvailable versions: {', '.join(changelog_data.keys())}",
                    color=0xff0000
                )
                await interaction.response.send_message(embed=embed, ephemeral=True)
        else:
            # Übersicht aller Versionen anzeigen
            await send_changelog_overview(interaction)

def setup(bot):
    register_update_commands(bot)